# Review Gate V2 - Complete Setup Guide

## 🎯 Overview
This guide will help you set up Review Gate V2 with Cursor IDE for enhanced code review capabilities.

## ✅ What's Already Done
- ✅ Python environment verified (Python 3.10.9)
- ✅ Standalone Review Gate server created
- ✅ AI rules configuration file created
- ✅ MCP server configuration prepared
- ✅ Cursor application opened

## 📋 Manual Configuration Steps

### Step 1: Configure Cursor AI Rules

1. **Open Cursor Settings**:
   - Press `Cmd + ,` (macOS) or `Ctrl + ,` (Windows/Linux)
   - Or go to Menu → Cursor → Settings

2. **Navigate to AI Rules**:
   - Look for "AI Rules" or "Rules" in the settings sidebar
   - If not visible, look under "AI" or "Features" section

3. **Copy AI Rules**:
   - Open the file: `cursor-extensions/review-gate-v2/ReviewGateV2.mdc`
   - Copy ALL content from this file
   - Paste it into the AI Rules text area in Cursor

4. **Save Settings**:
   - Click "Save" or "Apply"
   - The settings should be automatically saved

### Step 2: Configure MCP Server (Optional)

1. **Open Cursor Settings**:
   - Go to Settings → Extensions or MCP Servers

2. **Add MCP Server**:
   - Add a new MCP server with these settings:
   - **Name**: `review-gate-v2`
   - **Command**: `python3`
   - **Args**: `["/Users/<USER>/cursor-extensions/review-gate-v2/review_gate_standalone.py"]`

### Step 3: Restart Cursor

1. **Close Cursor completely**
2. **Reopen Cursor**
3. **Verify the AI rules are active**

## 🧪 Testing the Setup

### Test 1: Basic AI Rules
1. Open any code file in Cursor
2. Ask the AI to review your code
3. Check if the AI follows the Review Gate V2 guidelines

### Test 2: Interactive Review Gate
1. Open Terminal in the project directory
2. Run: `cd cursor-extensions/review-gate-v2`
3. Run: `python3 review_gate_standalone.py interactive`
4. Type `chat` and test the interactive mode

### Test 3: Trigger File System
1. Run: `python3 review_gate_standalone.py`
2. Check if trigger file is created in `/tmp/review_gate_trigger.json`
3. Check logs in `/tmp/review_gate_standalone.log`

## 📁 File Locations

- **AI Rules**: `cursor-extensions/review-gate-v2/ReviewGateV2.mdc`
- **Standalone Server**: `cursor-extensions/review-gate-v2/review_gate_standalone.py`
- **MCP Config**: `cursor-extensions/review-gate-v2/cursor_mcp_config.json`
- **Setup Script**: `cursor-extensions/review-gate-v2/setup_cursor.py`
- **Startup Script**: `cursor-extensions/review-gate-v2/start_review_gate.sh`

## 🔧 Troubleshooting

### Issue: AI Rules Not Working
- **Solution**: Check Cursor Settings → AI Rules
- Ensure the content from ReviewGateV2.mdc is properly pasted
- Restart Cursor after making changes

### Issue: MCP Server Not Connecting
- **Solution**: Check Python path and permissions
- Verify: `python3 review_gate_standalone.py` runs without errors
- Check logs in `/tmp/review_gate_standalone.log`

### Issue: Trigger Files Not Created
- **Solution**: Check `/tmp` directory permissions
- Verify Python can write to `/tmp`
- Run with elevated permissions if needed

## 🚀 Usage Examples

### Example 1: Code Review Request
```
Ask Cursor AI: "Please review this function according to Review Gate V2 standards"
```

### Example 2: Interactive Session
```bash
cd cursor-extensions/review-gate-v2
python3 review_gate_standalone.py interactive
> chat
Enter message: Please review my recent changes
```

### Example 3: Automated Trigger
```bash
python3 review_gate_standalone.py
# This creates a trigger file for Cursor extension to pick up
```

## 📊 System Status

- **Python Version**: 3.10.9 ✅
- **Review Gate Server**: Ready ✅
- **AI Rules File**: Created ✅
- **Cursor Application**: Opened ✅
- **Configuration**: Ready for manual setup ⏳

## 🎉 Next Steps

1. Follow the manual configuration steps above
2. Test the system with the provided examples
3. Start using Review Gate V2 for your code reviews!

---

**Need Help?** Check the log files or run the diagnostic commands provided in the troubleshooting section.
