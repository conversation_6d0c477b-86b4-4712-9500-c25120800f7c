#!/usr/bin/env python3
"""
Review Gate V2 - Simplified MCP Server
A basic version that works without external dependencies
"""

import asyncio
import json
import sys
import logging
import os
import time
import uuid
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Sequence

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleReviewGateServer:
    def __init__(self):
        self.shutdown_requested = False
        
    async def handle_request(self, request: dict) -> dict:
        """Handle MCP requests"""
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")
        
        logger.info(f"Received request: {method}")
        
        if method == "initialize":
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "serverInfo": {
                        "name": "review-gate-v2-simple",
                        "version": "2.0.0"
                    }
                }
            }
        elif method == "tools/list":
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "tools": [
                        {
                            "name": "review_gate_chat",
                            "description": "Open Review Gate chat popup in Cursor for feedback and reviews",
                            "inputSchema": {
                                "type": "object",
                                "properties": {
                                    "message": {
                                        "type": "string",
                                        "description": "The message to display in the Review Gate popup"
                                    },
                                    "title": {
                                        "type": "string",
                                        "description": "Title for the Review Gate popup window"
                                    }
                                },
                                "required": ["message"]
                            }
                        }
                    ]
                }
            }
        elif method == "tools/call":
            tool_name = params.get("name")
            arguments = params.get("arguments", {})
            
            if tool_name == "review_gate_chat":
                return await self.handle_review_gate_chat(request_id, arguments)
            else:
                return {
                    "jsonrpc": "2.0",
                    "id": request_id,
                    "error": {
                        "code": -32601,
                        "message": f"Method '{tool_name}' not found"
                    }
                }
        else:
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32601,
                    "message": f"Method '{method}' not found"
                }
            }
    
    async def handle_review_gate_chat(self, request_id: str, arguments: dict) -> dict:
        """Handle review_gate_chat tool call"""
        message = arguments.get("message", "Please provide your review or feedback:")
        title = arguments.get("title", "Review Gate V2 - ゲート")
        
        # Create trigger file for Cursor extension
        trigger_id = str(uuid.uuid4())
        trigger_file = f"/tmp/review_gate_trigger_{trigger_id}.json"
        
        trigger_data = {
            "trigger_id": trigger_id,
            "message": message,
            "title": title,
            "timestamp": datetime.now().isoformat(),
            "type": "review_gate_chat"
        }
        
        try:
            with open(trigger_file, 'w', encoding='utf-8') as f:
                json.dump(trigger_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Created trigger file: {trigger_file}")
            
            # Wait for user response
            response_file = f"/tmp/review_gate_response_{trigger_id}.json"
            timeout = 300  # 5 minutes
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                if os.path.exists(response_file):
                    try:
                        with open(response_file, 'r', encoding='utf-8') as f:
                            response_data = json.load(f)
                        
                        # Clean up files
                        os.remove(trigger_file)
                        os.remove(response_file)
                        
                        return {
                            "jsonrpc": "2.0",
                            "id": request_id,
                            "result": {
                                "content": [
                                    {
                                        "type": "text",
                                        "text": response_data.get("response", "No response received")
                                    }
                                ]
                            }
                        }
                    except Exception as e:
                        logger.error(f"Error reading response file: {e}")
                        break
                
                await asyncio.sleep(0.5)
            
            # Timeout - clean up and return default response
            if os.path.exists(trigger_file):
                os.remove(trigger_file)
            
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": "Review Gate timeout - no response received within 5 minutes"
                        }
                    ]
                }
            }
            
        except Exception as e:
            logger.error(f"Error in review_gate_chat: {e}")
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                }
            }
    
    async def run(self):
        """Run the MCP server"""
        logger.info("🚀 Review Gate V2 Simple server starting...")
        
        try:
            while not self.shutdown_requested:
                # Read request from stdin
                line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
                if not line:
                    break
                
                try:
                    request = json.loads(line.strip())
                    response = await self.handle_request(request)
                    
                    # Write response to stdout
                    response_line = json.dumps(response, ensure_ascii=False) + "\n"
                    await asyncio.get_event_loop().run_in_executor(None, lambda: sys.stdout.write(response_line))
                    await asyncio.get_event_loop().run_in_executor(None, sys.stdout.flush)
                    
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON: {e}")
                except Exception as e:
                    logger.error(f"Error processing request: {e}")
                    
        except KeyboardInterrupt:
            logger.info("Shutdown requested")
        except Exception as e:
            logger.error(f"Server error: {e}")
        finally:
            logger.info("Review Gate V2 Simple server stopped")

async def main():
    server = SimpleReviewGateServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main()) 