#!/bin/bash

# Review Gate V2 Startup Script
# This script starts the Review Gate V2 system for Cursor integration

echo "🚀 Starting Review Gate V2 System..."

# Set working directory
cd "$(dirname "$0")"

# Check Python version
echo "📋 Checking Python version..."
python3 --version

# Make sure log directory exists
mkdir -p /tmp

# Start the standalone server
echo "🎯 Starting Review Gate V2 Standalone Server..."
python3 review_gate_standalone.py interactive

echo "👋 Review Gate V2 stopped."
