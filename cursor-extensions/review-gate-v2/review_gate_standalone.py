#!/usr/bin/env python3
"""
Review Gate V2 - Standalone Version
A simplified version that works without external MCP dependencies
Designed for direct integration with Cursor IDE
"""

import asyncio
import json
import sys
import logging
import os
import time
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/tmp/review_gate_standalone.log'),
        logging.StreamHandler(sys.stderr)
    ]
)
logger = logging.getLogger(__name__)

class ReviewGateStandalone:
    def __init__(self):
        self.shutdown_requested = False
        self.active = True
        logger.info("🚀 Review Gate V2 Standalone initialized")
        
    def get_temp_path(self, filename: str) -> str:
        """Get cross-platform temporary file path"""
        if os.name == 'nt':  # Windows
            temp_dir = tempfile.gettempdir()
        else:  # macOS and Linux
            temp_dir = '/tmp'
        return os.path.join(temp_dir, filename)
    
    async def create_trigger_file(self, tool_name: str, data: dict) -> bool:
        """Create trigger file for Cursor extension"""
        try:
            trigger_file = Path(self.get_temp_path("review_gate_trigger.json"))
            
            trigger_data = {
                "timestamp": datetime.now().isoformat(),
                "system": "review-gate-v2-standalone",
                "editor": "cursor",
                "tool": tool_name,
                "data": data,
                "pid": os.getpid(),
                "active_window": True,
                "standalone_mode": True
            }
            
            logger.info(f"🎯 Creating trigger for {tool_name}")
            trigger_file.write_text(json.dumps(trigger_data, indent=2))
            
            # Verify file was created
            if trigger_file.exists():
                logger.info(f"✅ Trigger file created: {trigger_file}")
                return True
            else:
                logger.error(f"❌ Failed to create trigger file")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error creating trigger file: {e}")
            return False
    
    async def wait_for_response(self, trigger_id: str, timeout: int = 120) -> Optional[str]:
        """Wait for user response from Cursor extension"""
        response_patterns = [
            Path(self.get_temp_path(f"review_gate_response_{trigger_id}.json")),
            Path(self.get_temp_path("review_gate_response.json"))
        ]
        
        logger.info(f"👁️ Waiting for response (timeout: {timeout}s)")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                for response_file in response_patterns:
                    if response_file.exists():
                        try:
                            content = response_file.read_text().strip()
                            logger.info(f"📄 Found response: {content[:100]}...")
                            
                            if content.startswith('{'):
                                data = json.loads(content)
                                user_input = data.get("user_input", data.get("response", "")).strip()
                            else:
                                user_input = content
                            
                            # Clean up response file
                            try:
                                response_file.unlink()
                                logger.info("🧹 Response file cleaned up")
                            except:
                                pass
                            
                            if user_input:
                                logger.info(f"✅ Received user input: {user_input[:50]}...")
                                return user_input
                                
                        except Exception as e:
                            logger.error(f"❌ Error processing response: {e}")
                
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"❌ Error in wait loop: {e}")
                await asyncio.sleep(1)
        
        logger.warning(f"⏰ Timeout waiting for response")
        return None
    
    async def review_gate_chat(self, message: str = "Please provide your review:", 
                              title: str = "Review Gate V2", 
                              context: str = "", 
                              urgent: bool = False) -> str:
        """Main review gate chat function"""
        logger.info(f"💬 Starting review gate chat: {message[:50]}...")
        
        trigger_id = f"chat_{int(time.time() * 1000)}"
        
        success = await self.create_trigger_file("review_gate_chat", {
            "message": message,
            "title": title,
            "context": context,
            "urgent": urgent,
            "trigger_id": trigger_id
        })
        
        if success:
            response = await self.wait_for_response(trigger_id, timeout=300)  # 5 minutes
            if response:
                return f"✅ User Response: {response}"
            else:
                return "⏰ No response received within 5 minutes"
        else:
            return "❌ Failed to create review gate trigger"
    
    async def run_interactive(self):
        """Run interactive mode for testing"""
        logger.info("🎮 Starting interactive mode")
        print("Review Gate V2 Standalone - Interactive Mode")
        print("Commands: chat, quit")
        
        while not self.shutdown_requested:
            try:
                command = input("\n> ").strip().lower()
                
                if command == "quit":
                    break
                elif command == "chat":
                    message = input("Enter message: ").strip() or "Please provide your review:"
                    result = await self.review_gate_chat(message)
                    print(f"Result: {result}")
                else:
                    print("Unknown command. Use 'chat' or 'quit'")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"❌ Interactive error: {e}")
        
        logger.info("👋 Interactive mode ended")

async def main():
    """Main entry point"""
    rg = ReviewGateStandalone()
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        await rg.run_interactive()
    else:
        # Default: create a simple trigger for testing
        result = await rg.review_gate_chat(
            message="Review Gate V2 is now active! Please confirm you can see this popup.",
            title="Review Gate V2 - System Test"
        )
        print(result)

if __name__ == "__main__":
    asyncio.run(main())
