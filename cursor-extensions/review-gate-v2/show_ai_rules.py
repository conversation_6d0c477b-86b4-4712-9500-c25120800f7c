#!/usr/bin/env python3
"""
Display AI Rules for Copy-Paste into Cursor
"""

from pathlib import Path

def main():
    print("🎯 Review Gate V2 - AI Rules for Cursor")
    print("=" * 60)
    print("📋 Copy the content below and paste it into Cursor AI Rules:")
    print("=" * 60)
    print()
    
    rules_file = Path(__file__).parent / "ReviewGateV2.mdc"
    if rules_file.exists():
        content = rules_file.read_text()
        print(content)
        print()
        print("=" * 60)
        print("✅ Copy the above content to Cursor Settings → AI Rules")
        print("💡 Then restart Cursor to activate the rules")
    else:
        print("❌ ReviewGateV2.mdc file not found!")

if __name__ == "__main__":
    main()
