#!/usr/bin/env python3
"""
Cursor Setup Automation Script
Automatically configures Cursor with Review Gate V2 AI rules
"""

import os
import json
import subprocess
import time
from pathlib import Path

def get_cursor_config_path():
    """Get Cursor configuration directory path"""
    home = Path.home()
    
    # Common Cursor config paths
    possible_paths = [
        home / "Library" / "Application Support" / "Cursor" / "User",  # macOS
        home / ".config" / "cursor" / "User",  # Linux
        home / "AppData" / "Roaming" / "Cursor" / "User"  # Windows
    ]
    
    for path in possible_paths:
        if path.exists():
            return path
    
    return None

def read_ai_rules():
    """Read AI rules from ReviewGateV2.mdc"""
    rules_file = Path(__file__).parent / "ReviewGateV2.mdc"
    if rules_file.exists():
        return rules_file.read_text()
    return None

def setup_cursor_settings():
    """Setup Cursor settings with AI rules"""
    print("🔧 Setting up Cursor configuration...")
    
    config_path = get_cursor_config_path()
    if not config_path:
        print("❌ Could not find Cursor configuration directory")
        return False
    
    print(f"📁 Found Cursor config at: {config_path}")
    
    # Read AI rules
    ai_rules = read_ai_rules()
    if not ai_rules:
        print("❌ Could not read AI rules from ReviewGateV2.mdc")
        return False
    
    # Settings file path
    settings_file = config_path / "settings.json"
    
    # Read existing settings or create new
    settings = {}
    if settings_file.exists():
        try:
            settings = json.loads(settings_file.read_text())
            print("📖 Loaded existing Cursor settings")
        except:
            print("⚠️ Could not parse existing settings, creating new")
    
    # Add AI rules to settings
    settings["cursor.ai.rules"] = ai_rules
    settings["cursor.ai.enabled"] = True
    
    # Add MCP configuration if not exists
    if "cursor.mcp.servers" not in settings:
        mcp_config_file = Path(__file__).parent / "cursor_mcp_config.json"
        if mcp_config_file.exists():
            mcp_config = json.loads(mcp_config_file.read_text())
            settings.update(mcp_config)
            print("📋 Added MCP server configuration")
    
    # Write settings back
    try:
        settings_file.write_text(json.dumps(settings, indent=2))
        print("✅ Cursor settings updated successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to write settings: {e}")
        return False

def open_cursor():
    """Open Cursor application"""
    print("🚀 Opening Cursor...")
    
    try:
        # Try different ways to open Cursor
        if os.system("which cursor") == 0:
            subprocess.run(["cursor", "."], cwd=Path(__file__).parent.parent)
        elif Path("/Applications/Cursor.app").exists():
            subprocess.run(["open", "/Applications/Cursor.app"])
        else:
            print("⚠️ Could not find Cursor application")
            print("💡 Please open Cursor manually")
            return False
        
        print("✅ Cursor should be opening...")
        return True
        
    except Exception as e:
        print(f"❌ Error opening Cursor: {e}")
        return False

def main():
    """Main setup function"""
    print("🎯 Review Gate V2 - Cursor Setup Automation")
    print("=" * 50)
    
    # Step 1: Setup Cursor settings
    if setup_cursor_settings():
        print("✅ Step 1: Cursor settings configured")
    else:
        print("❌ Step 1: Failed to configure Cursor settings")
        return
    
    # Step 2: Open Cursor
    if open_cursor():
        print("✅ Step 2: Cursor opened")
    else:
        print("⚠️ Step 2: Please open Cursor manually")
    
    print("\n🎉 Setup completed!")
    print("\n📋 Next steps:")
    print("1. Cursor should restart automatically to load new settings")
    print("2. If not, please restart Cursor manually")
    print("3. The AI rules are now active in Cursor")
    print("4. You can test the Review Gate by running:")
    print("   python3 review_gate_standalone.py interactive")
    
    print("\n💡 Troubleshooting:")
    print("- Check Cursor Settings > AI Rules to verify configuration")
    print("- Look for Review Gate V2 rules in the AI Rules section")
    print("- Check the log file: /tmp/review_gate_standalone.log")

if __name__ == "__main__":
    main()
