#!/usr/bin/env python3
"""
Test script to verify Review Gate V2 configuration
"""

import json
import os
import sys
from pathlib import Path

def test_ai_rules():
    """Test if AI rules are properly configured"""
    print("🔍 Testing AI Rules Configuration...")
    
    settings_path = Path.home() / "Library/Application Support/Cursor/User/settings.json"
    if not settings_path.exists():
        print("❌ Cursor settings.json not found")
        return False
    
    try:
        with open(settings_path, 'r') as f:
            settings = json.load(f)
        
        if 'cursor.ai.rules' not in settings:
            print("❌ cursor.ai.rules not found in settings")
            return False
        
        rules = settings['cursor.ai.rules']
        if 'Review Gate V2' in rules and 'review_gate_chat' in rules:
            print("✅ AI Rules properly configured")
            print(f"   Rules length: {len(rules)} characters")
            return True
        else:
            print("❌ AI Rules missing Review Gate V2 content")
            return False
            
    except Exception as e:
        print(f"❌ Error reading settings: {e}")
        return False

def test_mcp_config():
    """Test if MCP configuration is properly set up"""
    print("\n🔍 Testing MCP Configuration...")
    
    # Check project-level config
    project_mcp = Path("../.cursor/mcp.json")
    if project_mcp.exists():
        try:
            with open(project_mcp, 'r') as f:
                config = json.load(f)
            
            if 'mcpServers' in config and 'review-gate-v2' in config['mcpServers']:
                server_config = config['mcpServers']['review-gate-v2']
                print("✅ Project MCP configuration found")
                print(f"   Command: {server_config.get('command', 'N/A')}")
                print(f"   Args: {server_config.get('args', 'N/A')}")
                return True
            else:
                print("❌ review-gate-v2 server not found in MCP config")
                return False
                
        except Exception as e:
            print(f"❌ Error reading MCP config: {e}")
            return False
    else:
        print("❌ MCP configuration file not found")
        return False

def test_mcp_server():
    """Test if MCP server can start"""
    print("\n🔍 Testing MCP Server...")
    
    server_path = Path("review_gate_v2_mcp.py")
    venv_python = Path("venv/bin/python")
    
    if not server_path.exists():
        print("❌ MCP server script not found")
        return False
    
    if not venv_python.exists():
        print("❌ Virtual environment Python not found")
        return False
    
    # Test import
    try:
        import subprocess
        result = subprocess.run([
            str(venv_python), "-c",
            "import mcp; print('MCP library imported successfully'); import sys; sys.exit(0)"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ MCP library available")
            print(f"   {result.stdout.strip()}")
            return True
        else:
            print("❌ MCP library test failed")
            print(f"   Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing MCP server: {e}")
        return False

def test_file_permissions():
    """Test file permissions and paths"""
    print("\n🔍 Testing File Permissions...")
    
    temp_dir = Path("/tmp")
    if not temp_dir.exists() or not os.access(temp_dir, os.W_OK):
        print("❌ Cannot write to /tmp directory")
        return False
    
    print("✅ Temporary directory writable")
    return True

def main():
    """Run all tests"""
    print("🎯 Review Gate V2 Configuration Test")
    print("=" * 50)
    
    tests = [
        ("AI Rules", test_ai_rules),
        ("MCP Config", test_mcp_config), 
        ("MCP Server", test_mcp_server),
        ("File Permissions", test_file_permissions)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Review Gate V2 should work in Cursor.")
        print("\n📋 Next steps:")
        print("1. Open Cursor")
        print("2. Check Settings → MCP Servers for 'review-gate-v2'")
        print("3. Try asking the AI to review some code")
        print("4. The AI should call review_gate_chat tool")
    else:
        print("❌ Some tests failed. Review Gate V2 may not work properly.")
        print("\n🔧 Troubleshooting:")
        print("- Check the failed tests above")
        print("- Ensure Cursor is restarted after configuration changes")
        print("- Check MCP server logs in /tmp/review_gate_v2.log")

if __name__ == "__main__":
    main()
